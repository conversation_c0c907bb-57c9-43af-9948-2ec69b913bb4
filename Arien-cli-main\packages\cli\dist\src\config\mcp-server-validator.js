/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { promisify } from 'util';
import { exec } from 'child_process';
const execAsync = promisify(exec);
/**
 * Validates MCP server configurations and their dependencies
 */
export class MCPServerValidator {
    static REQUIRED_COMMANDS = ['npx', 'uvx'];
    static COMMAND_INSTALL_INSTRUCTIONS = {
        npx: 'Install Node.js from https://nodejs.org/ (npx comes with Node.js)',
        uvx: 'Install uv from https://docs.astral.sh/uv/getting-started/installation/ (uvx comes with uv)',
    };
    /**
     * Validates a single MCP server configuration
     */
    static async validateMCPServer(serverName, config) {
        const result = {
            isValid: false,
            warnings: [],
            dependenciesInstalled: false,
            packageExists: false,
        };
        try {
            // Basic configuration validation
            if (!this.validateBasicConfig(config)) {
                result.error = 'Invalid basic configuration';
                return result;
            }
            // Check if required command is available
            if (config.command) {
                const depCheck = await this.checkCommandAvailability(config.command);
                if (!depCheck.available) {
                    result.error = `Command '${config.command}' not available. ${depCheck.installInstructions}`;
                    return result;
                }
                result.dependenciesInstalled = true;
                // Check if the package exists (for npm/uvx packages)
                if (config.args && config.args.length > 0) {
                    const packageCheck = await this.checkPackageExists(config.command, config.args);
                    result.packageExists = packageCheck.exists;
                    if (!packageCheck.exists) {
                        result.error = packageCheck.error;
                        return result;
                    }
                }
            }
            result.isValid = true;
            return result;
        }
        catch (error) {
            result.error = `Validation failed: ${error}`;
            return result;
        }
    }
    /**
     * Validates basic MCP server configuration structure
     */
    static validateBasicConfig(config) {
        if (!config)
            return false;
        // Check if at least one transport method is configured
        const hasStdio = !!config.command;
        const hasSSE = !!config.url;
        const hasHTTP = !!config.httpUrl;
        const hasTCP = !!config.tcp;
        return hasStdio || hasSSE || hasHTTP || hasTCP;
    }
    /**
     * Checks if a command is available in the system
     */
    static async checkCommandAvailability(command) {
        try {
            const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
            return {
                command,
                available: true,
                version: stdout.trim(),
            };
        }
        catch (error) {
            return {
                command,
                available: false,
                installInstructions: this.COMMAND_INSTALL_INSTRUCTIONS[command],
            };
        }
    }
    /**
     * Checks if an npm or uvx package exists and can be installed
     */
    static async checkPackageExists(command, args) {
        try {
            if (command === 'npx') {
                return await this.checkNpmPackage(args);
            }
            else if (command === 'uvx') {
                return await this.checkUvxPackage(args);
            }
            return { exists: true }; // For other commands, assume they exist
        }
        catch (error) {
            return { exists: false, error: `Package check failed: ${error}` };
        }
    }
    /**
     * Checks if an npm package exists
     */
    static async checkNpmPackage(args) {
        try {
            // Extract package name from args (skip flags like -y)
            const packageName = args.find(arg => !arg.startsWith('-'));
            if (!packageName) {
                return { exists: false, error: 'No package name found in arguments' };
            }
            // Remove version specifiers (@latest, @1.0.0, etc.)
            const cleanPackageName = packageName.split('@').slice(0, -1).join('@') || packageName;
            const { stdout } = await execAsync(`npm view ${cleanPackageName} version`, { timeout: 10000 });
            return { exists: !!stdout.trim() };
        }
        catch (error) {
            return { exists: false, error: `npm package not found or network error: ${error}` };
        }
    }
    /**
     * Checks if a uvx package exists
     */
    static async checkUvxPackage(args) {
        const packageName = args[0]; // First arg is usually the package name for uvx
        if (!packageName) {
            return { exists: false, error: 'No package name found in arguments' };
        }
        try {
            // Try to get package info using uvx
            const { stdout } = await execAsync(`uvx --help ${packageName}`, { timeout: 10000 });
            return { exists: true };
        }
        catch (error) {
            // If uvx --help fails, try a different approach
            try {
                // Try to run the package with --help to see if it exists
                const { stdout, stderr } = await execAsync(`uvx ${packageName} --help`, { timeout: 10000 });
                return { exists: true };
            }
            catch (secondError) {
                return { exists: false, error: `uvx package not found: ${packageName}` };
            }
        }
    }
    /**
     * Validates all built-in MCP servers and returns only the valid ones
     */
    static async validateBuiltInServers(servers) {
        const validServers = {};
        const validationPromises = Object.entries(servers).map(async ([name, config]) => {
            const result = await this.validateMCPServer(name, config);
            if (result.isValid) {
                validServers[name] = config;
                console.debug(`✅ MCP server '${name}' validated successfully`);
            }
            else {
                console.warn(`❌ MCP server '${name}' validation failed: ${result.error}`);
            }
        });
        await Promise.all(validationPromises);
        return validServers;
    }
    /**
     * Checks system dependencies and provides installation instructions
     */
    static async checkSystemDependencies() {
        const results = [];
        for (const command of this.REQUIRED_COMMANDS) {
            const result = await this.checkCommandAvailability(command);
            results.push(result);
        }
        return results;
    }
    /**
     * Attempts to auto-install missing dependencies
     */
    static async autoInstallDependencies() {
        try {
            const dependencies = await this.checkSystemDependencies();
            const missing = dependencies.filter(dep => !dep.available);
            if (missing.length === 0) {
                return { success: true, message: 'All dependencies are already installed' };
            }
            // For now, we'll just provide instructions rather than auto-install
            // Auto-installation can be dangerous and should require user consent
            const instructions = missing.map(dep => `- ${dep.command}: ${dep.installInstructions}`).join('\n');
            return {
                success: false,
                message: `Missing dependencies detected. Please install:\n${instructions}`
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to check dependencies: ${error}`
            };
        }
    }
}
//# sourceMappingURL=mcp-server-validator.js.map